<?php

namespace Tests\Unit;

use App\Models\Field;
use App\Models\Reservation;
use App\Services\FieldAvailabilityService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use InvalidArgumentException;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(\App\Services\FieldAvailabilityService::class)]
class FieldAvailabilityConflictTest extends TestCase
{
    use RefreshDatabase;

    protected FieldAvailabilityService $service;

    protected Field $field;

    protected function setUp(): void
    {
        parent::setUp();

        $this->service = new FieldAvailabilityService;
        $this->field = Field::factory()->create([
            'opening_time' => '08:00',
            'closing_time' => '22:00',
            'min_booking_hours' => 1,
            'max_booking_hours' => 8,
        ]);
    }

    #[Test]
    public function it_detects_no_conflict_for_adjacent_time_slots()
    {
        $date = now()->addDays(1)->format('Y-m-d');

        // Create existing reservation: 10:00-12:00
        Reservation::factory()->create([
            'field_id' => $this->field->id,
            'booking_date' => $date,
            'start_time' => '10:00',
            'end_time' => '12:00',
            'status' => 'Confirmed',
        ]);

        // Test adjacent slots should NOT conflict
        $this->assertFalse($this->service->hasConflictingReservations($this->field, $date, '12:00', '14:00')); // Starts when existing ends
        $this->assertFalse($this->service->hasConflictingReservations($this->field, $date, '08:00', '10:00')); // Ends when existing starts
    }

    #[Test]
    public function it_detects_overlapping_conflicts_correctly()
    {
        $date = now()->addDays(1)->format('Y-m-d');

        // Create existing reservation: 10:00-12:00
        $reservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'booking_date' => $date,
            'start_time' => '10:00',
            'end_time' => '12:00',
            'status' => 'Confirmed',
        ]);

        // Test overlapping scenarios that SHOULD conflict
        $this->assertTrue($this->service->hasConflictingReservations($this->field, $date, '09:00', '11:00')); // Overlaps start
        $this->assertTrue($this->service->hasConflictingReservations($this->field, $date, '11:00', '13:00')); // Overlaps end
        $this->assertTrue($this->service->hasConflictingReservations($this->field, $date, '10:30', '11:30')); // Contained within
        $this->assertTrue($this->service->hasConflictingReservations($this->field, $date, '09:00', '13:00')); // Contains existing
        $this->assertTrue($this->service->hasConflictingReservations($this->field, $date, '10:00', '12:00')); // Exact match
        $this->assertTrue($this->service->hasConflictingReservations($this->field, $date, '09:30', '10:30')); // Overlaps start by 30 min
        $this->assertTrue($this->service->hasConflictingReservations($this->field, $date, '11:30', '12:30')); // Overlaps end by 30 min
    }

    #[Test]
    public function it_detects_no_conflict_for_non_overlapping_slots()
    {
        $date = now()->addDays(1)->format('Y-m-d');

        // Create existing reservation: 10:00-12:00
        Reservation::factory()->create([
            'field_id' => $this->field->id,
            'booking_date' => $date,
            'start_time' => '10:00',
            'end_time' => '12:00',
            'status' => 'Confirmed',
        ]);

        // Test non-overlapping scenarios that should NOT conflict
        $this->assertFalse($this->service->hasConflictingReservations($this->field, $date, '08:00', '09:00')); // Before existing
        $this->assertFalse($this->service->hasConflictingReservations($this->field, $date, '13:00', '15:00')); // After existing
        $this->assertFalse($this->service->hasConflictingReservations($this->field, $date, '07:00', '08:30')); // Well before
        $this->assertFalse($this->service->hasConflictingReservations($this->field, $date, '14:00', '16:00')); // Well after
    }

    #[Test]
    public function it_excludes_specified_reservation_from_conflict_check()
    {
        $date = now()->addDays(1)->format('Y-m-d');

        // Create existing reservation: 10:00-12:00
        $existingReservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'booking_date' => $date,
            'start_time' => '10:00',
            'end_time' => '12:00',
            'status' => 'Confirmed',
        ]);

        // Test that excluding the reservation ID prevents conflict detection
        $this->assertFalse($this->service->hasConflictingReservations(
            $this->field,
            $date,
            '10:00',
            '12:00',
            $existingReservation->id
        ));

        // Test that it still detects conflicts with other reservations
        $anotherReservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'booking_date' => $date,
            'start_time' => '14:00',
            'end_time' => '16:00',
            'status' => 'Confirmed',
        ]);

        $this->assertTrue($this->service->hasConflictingReservations(
            $this->field,
            $date,
            '15:00',
            '17:00',
            $existingReservation->id // Excluding first reservation, but should still conflict with second
        ));
    }

    #[Test]
    public function it_ignores_cancelled_reservations()
    {
        $date = now()->addDays(1)->format('Y-m-d');

        // Create cancelled reservation: 10:00-12:00
        Reservation::factory()->create([
            'field_id' => $this->field->id,
            'booking_date' => $date,
            'start_time' => '10:00',
            'end_time' => '12:00',
            'status' => 'Cancelled',
        ]);

        // Should not conflict with cancelled reservation
        $this->assertFalse($this->service->hasConflictingReservations($this->field, $date, '10:00', '12:00'));
        $this->assertFalse($this->service->hasConflictingReservations($this->field, $date, '11:00', '13:00'));
    }

    #[Test]
    public function it_validates_time_format_inputs()
    {
        $date = now()->addDays(1)->format('Y-m-d');

        // Test invalid time formats
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid start time format');
        $this->service->hasConflictingReservations($this->field, $date, '25:00', '12:00');
    }

    #[Test]
    public function it_validates_end_time_format()
    {
        $date = now()->addDays(1)->format('Y-m-d');

        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid end time format');
        $this->service->hasConflictingReservations($this->field, $date, '10:00', '12:70');
    }

    #[Test]
    public function it_validates_time_order()
    {
        $date = now()->addDays(1)->format('Y-m-d');

        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('End time (10:00) must be after start time (12:00)');
        $this->service->hasConflictingReservations($this->field, $date, '12:00', '10:00');
    }

    #[Test]
    public function it_validates_same_start_and_end_time()
    {
        $date = now()->addDays(1)->format('Y-m-d');

        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('End time (10:00) must be after start time (10:00)');
        $this->service->hasConflictingReservations($this->field, $date, '10:00', '10:00');
    }

    #[Test]
    public function it_handles_multiple_existing_reservations()
    {
        $date = now()->addDays(1)->format('Y-m-d');

        // Create multiple existing reservations
        Reservation::factory()->create([
            'field_id' => $this->field->id,
            'booking_date' => $date,
            'start_time' => '09:00',
            'end_time' => '11:00',
            'status' => 'Confirmed',
        ]);

        Reservation::factory()->create([
            'field_id' => $this->field->id,
            'booking_date' => $date,
            'start_time' => '14:00',
            'end_time' => '16:00',
            'status' => 'Confirmed',
        ]);

        // Test conflicts with first reservation
        $this->assertTrue($this->service->hasConflictingReservations($this->field, $date, '10:00', '12:00'));

        // Test conflicts with second reservation
        $this->assertTrue($this->service->hasConflictingReservations($this->field, $date, '15:00', '17:00'));

        // Test slot between reservations (should not conflict)
        $this->assertFalse($this->service->hasConflictingReservations($this->field, $date, '12:00', '14:00'));

        // Test slot that spans both reservations (should conflict)
        $this->assertTrue($this->service->hasConflictingReservations($this->field, $date, '08:00', '18:00'));
    }

    #[Test]
    public function it_only_checks_conflicts_for_same_field()
    {
        $date = now()->addDays(1)->format('Y-m-d');
        $otherField = Field::factory()->create();

        // Create reservation on different field
        Reservation::factory()->create([
            'field_id' => $otherField->id,
            'booking_date' => $date,
            'start_time' => '10:00',
            'end_time' => '12:00',
            'status' => 'Confirmed',
        ]);

        // Should not conflict with reservation on different field
        $this->assertFalse($this->service->hasConflictingReservations($this->field, $date, '10:00', '12:00'));
    }

    #[Test]
    public function it_only_checks_conflicts_for_same_date()
    {
        $date = now()->addDays(1)->format('Y-m-d');
        $differentDate = now()->addDays(2)->format('Y-m-d');

        // Create reservation on different date
        Reservation::factory()->create([
            'field_id' => $this->field->id,
            'booking_date' => $differentDate,
            'start_time' => '10:00',
            'end_time' => '12:00',
            'status' => 'Confirmed',
        ]);

        // Should not conflict with reservation on different date
        $this->assertFalse($this->service->hasConflictingReservations($this->field, $date, '10:00', '12:00'));
    }

    #[Test]
    public function it_accepts_various_valid_time_formats()
    {
        $date = now()->addDays(1)->format('Y-m-d');

        // Test various valid time formats
        $this->assertFalse($this->service->hasConflictingReservations($this->field, $date, '9:00', '11:00')); // Single digit hour
        $this->assertFalse($this->service->hasConflictingReservations($this->field, $date, '09:00', '11:00')); // Zero-padded hour
        $this->assertFalse($this->service->hasConflictingReservations($this->field, $date, '00:00', '01:00')); // Midnight
        $this->assertFalse($this->service->hasConflictingReservations($this->field, $date, '23:00', '23:59')); // Late evening
    }

    #[Test]
    public function it_handles_edge_case_boundary_times()
    {
        $date = now()->addDays(1)->format('Y-m-d');

        // Create existing reservation: 12:00-13:00
        Reservation::factory()->create([
            'field_id' => $this->field->id,
            'booking_date' => $date,
            'start_time' => '12:00',
            'end_time' => '13:00',
            'status' => 'Confirmed',
        ]);

        // Test exact boundary conditions
        $this->assertFalse($this->service->hasConflictingReservations($this->field, $date, '11:00', '12:00')); // Ends exactly when existing starts
        $this->assertFalse($this->service->hasConflictingReservations($this->field, $date, '13:00', '14:00')); // Starts exactly when existing ends
        $this->assertTrue($this->service->hasConflictingReservations($this->field, $date, '11:59', '12:01')); // Overlaps by 1 minute
        $this->assertTrue($this->service->hasConflictingReservations($this->field, $date, '12:59', '13:01')); // Overlaps by 1 minute
    }
}
