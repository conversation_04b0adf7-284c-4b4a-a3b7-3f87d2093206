<?php

namespace Tests\Unit;

use App\Models\Amenity;
use App\Models\Field;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(Field::class)]
class FieldModelTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function field_has_correct_fillable_attributes()
    {
        $field = new Field;
        $fillable = $field->getFillable();

        $expectedFillable = [
            'name', 'type', 'description', 'hourly_rate', 'night_hourly_rate',
            'night_time_start', 'capacity', 'status', 'opening_time', 'closing_time',
            'min_booking_hours', 'max_booking_hours',
        ];

        foreach ($expectedFillable as $attribute) {
            $this->assertContains($attribute, $fillable);
        }
    }

    #[Test]
    public function field_has_correct_casts()
    {
        $field = Field::factory()->create([
            'hourly_rate' => '75.50',
            'night_hourly_rate' => '85.75',
        ]);

        // Test decimal casting
        $this->assertIsString($field->hourly_rate); // Laravel decimal cast returns string
        $this->assertEquals('75.50', $field->hourly_rate);
        $this->assertIsString($field->night_hourly_rate);
        $this->assertEquals('85.75', $field->night_hourly_rate);
    }

    #[Test]
    public function amenities_relationship_returns_belongs_to_many()
    {
        $field = Field::factory()->create();

        $relationship = $field->amenities();

        $this->assertInstanceOf(BelongsToMany::class, $relationship);
        $this->assertEquals('field_amenity', $relationship->getTable());
    }

    #[Test]
    public function amenities_relationship_returns_collection_of_amenity_objects()
    {
        $field = Field::factory()->create();
        $amenity1 = Amenity::factory()->create(['name' => 'Lighting']);
        $amenity2 = Amenity::factory()->create(['name' => 'Parking']);

        // Attach amenities to field
        $field->amenities()->attach([$amenity1->id, $amenity2->id]);

        $amenities = $field->amenities;

        $this->assertInstanceOf(Collection::class, $amenities);
        $this->assertCount(2, $amenities);
        $this->assertInstanceOf(Amenity::class, $amenities->first());
        $this->assertTrue($amenities->contains($amenity1));
        $this->assertTrue($amenities->contains($amenity2));
    }

    #[Test]
    public function amenities_collection_contains_proper_amenity_objects_not_arrays()
    {
        $field = Field::factory()->create();
        $amenity = Amenity::factory()->create([
            'name' => 'Test Amenity',
            'icon_class' => 'ri-test-line',
            'is_active' => true,
        ]);

        $field->amenities()->attach($amenity->id);
        $amenities = $field->amenities;

        $firstAmenity = $amenities->first();

        // Verify it's an Amenity object with proper properties
        $this->assertInstanceOf(Amenity::class, $firstAmenity);
        $this->assertEquals('Test Amenity', $firstAmenity->name);
        $this->assertEquals('ri-test-line', $firstAmenity->icon_class);
        $this->assertTrue($firstAmenity->is_active);

        // Verify it's NOT an array
        $this->assertIsNotArray($firstAmenity);

        // Verify object properties are accessible via magic methods
        $this->assertIsString($firstAmenity->name);
        $this->assertIsString($firstAmenity->icon_class);
        $this->assertIsBool($firstAmenity->is_active);
    }

    #[Test]
    public function get_available_amenities_returns_proper_array_structure()
    {
        // Create some amenities
        $amenity1 = Amenity::factory()->create(['name' => 'Lighting', 'is_active' => true]);
        $amenity2 = Amenity::factory()->create(['name' => 'Parking', 'is_active' => true]);
        $amenity3 = Amenity::factory()->create(['name' => 'Inactive', 'is_active' => false]);

        $availableAmenities = Field::getAvailableAmenities();

        // Should be an array
        $this->assertIsArray($availableAmenities);

        // Should have ID as key and name as value for active amenities
        $this->assertArrayHasKey($amenity1->id, $availableAmenities);
        $this->assertArrayHasKey($amenity2->id, $availableAmenities);
        $this->assertEquals('Lighting', $availableAmenities[$amenity1->id]);
        $this->assertEquals('Parking', $availableAmenities[$amenity2->id]);

        // Should NOT include inactive amenities
        $this->assertArrayNotHasKey($amenity3->id, $availableAmenities);
    }

    #[Test]
    public function get_available_amenities_handles_database_exception()
    {
        // This test ensures the method works normally when database is available
        // Create some amenities first
        $amenity1 = Amenity::factory()->create(['name' => 'Lighting', 'is_active' => true]);
        $amenity2 = Amenity::factory()->create(['name' => 'Parking', 'is_active' => true]);

        $availableAmenities = Field::getAvailableAmenities();

        // Should return an array with ID keys and name values
        $this->assertIsArray($availableAmenities);
        $this->assertArrayHasKey($amenity1->id, $availableAmenities);
        $this->assertArrayHasKey($amenity2->id, $availableAmenities);
        $this->assertEquals('Lighting', $availableAmenities[$amenity1->id]);
        $this->assertEquals('Parking', $availableAmenities[$amenity2->id]);
    }

    #[Test]
    public function get_formatted_amenities_attribute_returns_correct_string()
    {
        $field = Field::factory()->create();

        // Test with no amenities
        $this->assertEquals('None', $field->formatted_amenities);

        // Test with amenities
        $amenity1 = Amenity::factory()->create(['name' => 'Lighting']);
        $amenity2 = Amenity::factory()->create(['name' => 'Parking']);
        $field->amenities()->attach([$amenity1->id, $amenity2->id]);
        $field->refresh();

        $formattedAmenities = $field->formatted_amenities;
        $this->assertIsString($formattedAmenities);
        $this->assertStringContainsString('Lighting', $formattedAmenities);
        $this->assertStringContainsString('Parking', $formattedAmenities);
        $this->assertStringContainsString(', ', $formattedAmenities);
    }

    #[Test]
    public function formatted_amenities_handles_empty_collection()
    {
        $field = Field::factory()->create();

        // Ensure no amenities are attached
        $this->assertEquals(0, $field->amenities->count());

        // Should return 'None'
        $this->assertEquals('None', $field->formatted_amenities);
    }

    #[Test]
    public function active_scope_returns_only_active_fields()
    {
        Field::factory()->create(['status' => 'Active', 'name' => 'Active Field']);
        Field::factory()->create(['status' => 'Inactive', 'name' => 'Inactive Field']);
        Field::factory()->create(['status' => 'Under Maintenance', 'name' => 'Maintenance Field']);

        $activeFields = Field::active()->get();

        $this->assertEquals(1, $activeFields->count());
        $this->assertEquals('Active', $activeFields->first()->status);
        $this->assertEquals('Active Field', $activeFields->first()->name);
    }

    #[Test]
    public function available_scope_returns_available_fields()
    {
        Field::factory()->create(['status' => 'Active', 'name' => 'Active Field']);
        Field::factory()->create(['status' => 'Inactive', 'name' => 'Inactive Field']);
        Field::factory()->create(['status' => 'Under Maintenance', 'name' => 'Maintenance Field']);

        $availableFields = Field::available()->get();

        $this->assertEquals(1, $availableFields->count());
        $this->assertEquals('Active', $availableFields->first()->status);
        $this->assertEquals('Active Field', $availableFields->first()->name);
    }

    #[Test]
    public function field_amenities_cannot_be_accessed_with_array_syntax()
    {
        $field = Field::factory()->create();
        $amenity = Amenity::factory()->create(['name' => 'Test Amenity']);
        $field->amenities()->attach($amenity->id);

        $amenities = $field->amenities;
        $firstAmenity = $amenities->first();

        // This should work (object property access)
        $this->assertEquals('Test Amenity', $firstAmenity->name);

        // Test that array access doesn't work by checking if it's not an array
        // In modern PHP, trying to access object properties with array syntax
        // might not always throw an exception, so we'll test the type instead
        $this->assertFalse(is_array($firstAmenity));
        $this->assertTrue(is_object($firstAmenity));

        // Verify that proper object access works
        $this->assertIsString($firstAmenity->name);
        $this->assertEquals('Test Amenity', $firstAmenity->name);
    }

    #[Test]
    public function amenity_objects_in_collection_are_not_arrays()
    {
        $field = Field::factory()->create();
        $amenity1 = Amenity::factory()->create(['name' => 'Lighting']);
        $amenity2 = Amenity::factory()->create(['name' => 'Parking']);
        $field->amenities()->attach([$amenity1->id, $amenity2->id]);

        $amenities = $field->amenities;

        foreach ($amenities as $amenity) {
            // Each amenity should be an object, not an array
            $this->assertIsObject($amenity);
            $this->assertInstanceOf(Amenity::class, $amenity);
            $this->assertIsNotArray($amenity);

            // Should have object properties
            $this->assertIsString($amenity->name);
            $this->assertIsString($amenity->icon_class);
            $this->assertIsBool($amenity->is_active);
        }
    }

    #[Test]
    public function field_amenities_relationship_pivot_table_works()
    {
        $field = Field::factory()->create();
        $amenity = Amenity::factory()->create();

        // Test attaching
        $field->amenities()->attach($amenity->id);
        $this->assertTrue($field->amenities->contains($amenity));

        // Test detaching
        $field->amenities()->detach($amenity->id);
        $field->refresh();
        $this->assertFalse($field->amenities->contains($amenity));

        // Test sync
        $amenity2 = Amenity::factory()->create();
        $field->amenities()->sync([$amenity->id, $amenity2->id]);
        $field->refresh();
        $this->assertEquals(2, $field->amenities->count());
    }

    #[Test]
    public function field_can_have_multiple_amenities_and_amenity_can_have_multiple_fields()
    {
        $field1 = Field::factory()->create(['name' => 'Field 1']);
        $field2 = Field::factory()->create(['name' => 'Field 2']);
        $amenity = Amenity::factory()->create(['name' => 'Shared Amenity']);

        // Attach same amenity to both fields
        $field1->amenities()->attach($amenity->id);
        $field2->amenities()->attach($amenity->id);

        // Verify many-to-many relationship
        $this->assertTrue($field1->amenities->contains($amenity));
        $this->assertTrue($field2->amenities->contains($amenity));
        $this->assertTrue($amenity->fields->contains($field1));
        $this->assertTrue($amenity->fields->contains($field2));
        $this->assertEquals(2, $amenity->fields->count());
    }
}
