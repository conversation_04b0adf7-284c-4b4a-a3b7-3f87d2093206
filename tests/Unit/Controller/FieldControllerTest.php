<?php

namespace Tests\Unit\Http\Controllers\Admin;

use App\Http\Controllers\Admin\FieldController;
use App\Models\Amenity;
use App\Models\Field;
use App\Models\Reservation;
use App\Models\User;
use App\Models\Utility;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Mockery;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(FieldController::class)]
class FieldControllerTest extends TestCase
{
    use RefreshDatabase;

    protected FieldController $controller;

    protected function setUp(): void
    {
        parent::setUp();
        $this->controller = new FieldController;
    }

    #[Test]
    public function index_returns_view_with_fields()
    {
        // Create test fields
        Field::factory()->count(3)->create();

        // Create a request with no search parameters
        $request = new Request;

        // Call the controller method
        $response = $this->controller->index($request);

        // Assert the response is a view
        $this->assertEquals('admin.fields.index', $response->name());

        // Assert the view has fields data
        $this->assertArrayHasKey('fields', $response->getData());

        // Assert the fields are paginated
        $fields = $response->getData()['fields'];
        $this->assertInstanceOf(\Illuminate\Pagination\LengthAwarePaginator::class, $fields);

        // Assert we have 3 fields
        $this->assertEquals(3, $fields->count());
    }

    #[Test]
    public function index_filters_by_search_term()
    {
        // Create test fields with specific names
        Field::factory()->create(['name' => 'Soccer Field A']);
        Field::factory()->create(['name' => 'Basketball Court']);
        Field::factory()->create(['name' => 'Soccer Field B']);

        // Create a request with search parameter
        $request = new Request(['search' => 'Soccer']);

        // Call the controller method
        $response = $this->controller->index($request);

        // Get the fields from the response
        $fields = $response->getData()['fields'];

        // Assert we have 2 fields matching the search
        $this->assertEquals(2, $fields->count());

        // Assert the fields have the correct names
        $fieldNames = $fields->pluck('name')->toArray();
        $this->assertContains('Soccer Field A', $fieldNames);
        $this->assertContains('Soccer Field B', $fieldNames);
        $this->assertNotContains('Basketball Court', $fieldNames);
    }

    #[Test]
    public function index_filters_by_type()
    {
        // Create test fields with different types
        Field::factory()->create(['type' => 'Soccer']);
        Field::factory()->create(['type' => 'Basketball']);
        Field::factory()->create(['type' => 'Soccer']);

        // Create a request with type filter
        $request = new Request(['type' => 'Soccer']);

        // Call the controller method
        $response = $this->controller->index($request);

        // Get the fields from the response
        $fields = $response->getData()['fields'];

        // Assert we have 2 fields matching the type
        $this->assertEquals(2, $fields->count());

        // Assert all fields have the correct type
        foreach ($fields as $field) {
            $this->assertEquals('Soccer', $field->type);
        }
    }

    #[Test]
    public function index_filters_by_status()
    {
        // Create test fields with different statuses
        Field::factory()->create(['status' => 'Active']);
        Field::factory()->create(['status' => 'Inactive']);
        Field::factory()->create(['status' => 'Active']);

        // Create a request with status filter
        $request = new Request(['status' => 'Active']);

        // Call the controller method
        $response = $this->controller->index($request);

        // Get the fields from the response
        $fields = $response->getData()['fields'];

        // Assert we have 2 fields matching the status
        $this->assertEquals(2, $fields->count());

        // Assert all fields have the correct status
        foreach ($fields as $field) {
            $this->assertEquals('Active', $field->status);
        }
    }

    #[Test]
    public function create_returns_create_view()
    {
        // Call the controller method
        $response = $this->controller->create();

        // Assert the response is the correct view
        $this->assertEquals('admin.fields.create', $response->name());
    }

    #[Test]
    public function show_returns_view_with_field_and_relationships()
    {
        // Create a field with relationships
        $field = Field::factory()->create();
        $amenities = Amenity::factory()->count(2)->create();
        $utilities = Utility::factory()->count(2)->create();
        $field->amenities()->attach($amenities->pluck('id'));
        $field->utilities()->attach($utilities->pluck('id'));

        // Create some bookings for the field
        $user = User::factory()->create();
        Reservation::factory()->count(3)->create([
            'field_id' => $field->id,
            'user_id' => $user->id,
        ]);

        // Call the controller method
        $response = $this->controller->show($field);

        // Assert the response is the correct view
        $this->assertEquals('admin.fields.show', $response->name());

        // Assert the view has the field data
        $this->assertArrayHasKey('field', $response->getData());

        // Get the field from the response
        $responseField = $response->getData()['field'];

        // Assert the field has the correct relationships loaded
        $this->assertTrue($responseField->relationLoaded('bookings'));
        $this->assertTrue($responseField->relationLoaded('amenities'));
        $this->assertTrue($responseField->relationLoaded('utilities'));

        // Assert the bookings relationship has the user relationship loaded
        $this->assertTrue($responseField->bookings->first()->relationLoaded('user'));
    }

    #[Test]
    public function edit_returns_view_with_field_and_relationships()
    {
        // Create a field with relationships
        $field = Field::factory()->create();
        $amenities = Amenity::factory()->count(2)->create();
        $utilities = Utility::factory()->count(2)->create();
        $field->amenities()->attach($amenities->pluck('id'));
        $field->utilities()->attach($utilities->pluck('id'));

        // Call the controller method
        $response = $this->controller->edit($field);

        // Assert the response is the correct view
        $this->assertEquals('admin.fields.edit', $response->name());

        // Assert the view has the field data
        $this->assertArrayHasKey('field', $response->getData());

        // Get the field from the response
        $responseField = $response->getData()['field'];

        // Assert the field has the correct relationships loaded
        $this->assertTrue($responseField->relationLoaded('amenities'));
        $this->assertTrue($responseField->relationLoaded('utilities'));
    }

    #[Test]
    public function destroy_deletes_field_when_no_active_bookings()
    {
        // Create a field
        $field = Field::factory()->create([
            'name' => 'Field to Delete',
        ]);

        // Mock the activeBookings relationship to return empty collection
        $field = Mockery::mock($field)->makePartial();
        $field->shouldReceive('activeBookings->count')->andReturn(0);

        // Call the controller method
        $response = $this->controller->destroy($field);

        // Assert the response is a redirect
        $this->assertInstanceOf(\Illuminate\Http\RedirectResponse::class, $response);
    }

    #[Test]
    public function destroy_prevents_deletion_when_field_has_active_bookings()
    {
        // Create a field
        $field = Field::factory()->create([
            'name' => 'Field with Bookings',
        ]);

        // Mock the activeBookings relationship to return count > 0
        $field = Mockery::mock($field)->makePartial();
        $field->shouldReceive('activeBookings->count')->andReturn(2);

        // Call the controller method
        $response = $this->controller->destroy($field);

        // Assert the response is a redirect back
        $this->assertInstanceOf(\Illuminate\Http\RedirectResponse::class, $response);
    }

    #[Test]
    public function index_includes_active_bookings_count()
    {
        // Create test fields
        $field1 = Field::factory()->create();
        $field2 = Field::factory()->create();

        // Create active bookings for field1
        $user = User::factory()->create();
        Reservation::factory()->count(3)->create([
            'field_id' => $field1->id,
            'user_id' => $user->id,
            'status' => 'Confirmed',
        ]);

        // Create a request
        $request = new Request;

        // Call the controller method
        $response = $this->controller->index($request);

        // Get the fields from the response
        $fields = $response->getData()['fields'];

        // Find our test fields in the results
        $testField1 = $fields->where('id', $field1->id)->first();
        $testField2 = $fields->where('id', $field2->id)->first();

        // Assert the active_bookings_count is loaded
        $this->assertNotNull($testField1);
        $this->assertNotNull($testField2);
        $this->assertTrue(isset($testField1->active_bookings_count));
        $this->assertTrue(isset($testField2->active_bookings_count));
    }

    #[Test]
    public function store_sets_default_night_time_start_when_not_provided()
    {
        // Create a valid request without night_time_start
        $requestData = [
            'name' => 'Test Field',
            'type' => 'Soccer',
            'description' => 'Test description',
            'hourly_rate' => 50.00,
            'capacity' => 20,
            'status' => 'Active',
        ];

        $request = new Request($requestData);

        // Call the controller method
        $this->controller->store($request);

        // Assert the field was created with default night_time_start
        $this->assertDatabaseHas('fields', [
            'name' => 'Test Field',
            'night_time_start' => '18:00',
        ]);
    }

    #[Test]
    public function update_sets_default_night_time_start_when_not_provided()
    {
        // Create a field
        $field = Field::factory()->create([
            'name' => 'Test Field',
            'night_time_start' => '20:00',
        ]);

        // Create a valid request without night_time_start
        $requestData = [
            'name' => 'Updated Field',
            'type' => 'Soccer',
            'description' => 'Updated description',
            'hourly_rate' => 50.00,
            'capacity' => 20,
            'status' => 'Active',
            'night_time_start' => null,
        ];

        $request = new Request($requestData);

        // Call the controller method
        $this->controller->update($request, $field);

        // Refresh the field from the database
        $field->refresh();

        // Assert the field was updated with default night_time_start
        $this->assertEquals('18:00', $field->night_time_start);
    }

    #[Test]
    public function store_syncs_empty_amenities_and_utilities_when_not_provided()
    {
        // Create a valid request without amenities and utilities
        $requestData = [
            'name' => 'Test Field',
            'type' => 'Soccer',
            'description' => 'Test description',
            'hourly_rate' => 50.00,
            'capacity' => 20,
            'status' => 'Active',
        ];

        $request = new Request($requestData);

        // Call the controller method
        $this->controller->store($request);

        // Get the created field
        $field = Field::where('name', 'Test Field')->first();

        // Assert no relationships were synced
        $this->assertCount(0, $field->amenities);
        $this->assertCount(0, $field->utilities);
    }

    #[Test]
    public function update_syncs_empty_amenities_and_utilities_when_not_provided()
    {
        // Create a field with existing relationships
        $field = Field::factory()->create();
        $amenities = Amenity::factory()->count(2)->create();
        $utilities = Utility::factory()->count(2)->create();
        $field->amenities()->attach($amenities->pluck('id'));
        $field->utilities()->attach($utilities->pluck('id'));

        // Create a valid request without amenities and utilities
        $requestData = [
            'name' => 'Updated Field',
            'type' => 'Soccer',
            'description' => 'Updated description',
            'hourly_rate' => 50.00,
            'capacity' => 20,
            'status' => 'Active',
        ];

        $request = new Request($requestData);

        // Call the controller method
        $this->controller->update($request, $field);

        // Refresh the field from the database
        $field->refresh();

        // Assert relationships were cleared
        $this->assertCount(0, $field->amenities);
        $this->assertCount(0, $field->utilities);
    }
}
