<?php

namespace Tests\Feature;

use App\Models\Amenity;
use App\Models\Field;
use App\Models\Reservation;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class AmenityFieldEdgeCasesTest extends TestCase
{
    use RefreshDatabase;

    protected User $client;

    protected User $admin;

    protected function setUp(): void
    {
        parent::setUp();

        $this->client = User::factory()->create(['role' => 'user']);
        $this->admin = User::factory()->create(['role' => 'admin']);
    }

    #[Test]
    public function field_with_large_number_of_amenities_renders_correctly()
    {
        $field = Field::factory()->create();

        // Create many amenities
        $amenities = [];
        for ($i = 1; $i <= 50; $i++) {
            $amenities[] = Amenity::factory()->create([
                'name' => "Amenity $i",
                'icon_class' => 'ri-star-line',
            ]);
        }

        $field->amenities()->attach($amenities);

        $booking = Reservation::factory()->create([
            'field_id' => $field->id,
            'user_id' => $this->client->id,
        ]);

        $response = $this->actingAs($this->client)
            ->get(route('bookings.show', $booking));

        $response->assertStatus(200);
        $response->assertDontSee('Amenity 1'); // Amenities not displayed (feature removed)
        $response->assertDontSee('Amenity 50');
        $response->assertDontSee('TypeError');
        $response->assertDontSee('Memory limit');
    }

    #[Test]
    public function amenity_with_special_characters_in_name_handled_correctly()
    {
        $field = Field::factory()->create();
        $amenity = Amenity::factory()->create([
            'name' => 'Café & Restaurant (24/7)',
            'icon_class' => 'ri-restaurant-line',
        ]);

        $field->amenities()->attach($amenity->id);

        $booking = Reservation::factory()->create([
            'field_id' => $field->id,
            'user_id' => $this->client->id,
        ]);

        $response = $this->actingAs($this->client)
            ->get(route('bookings.show', $booking));

        $response->assertStatus(200);
        $response->assertDontSee('Café & Restaurant (24/7)'); // Amenities not displayed (feature removed)
        $response->assertDontSee('TypeError');
    }

    #[Test]
    public function amenity_with_very_long_name_handled_correctly()
    {
        $field = Field::factory()->create();
        $longName = str_repeat('Very Long Amenity Name ', 20); // ~400 characters
        $amenity = Amenity::factory()->create([
            'name' => $longName,
            'icon_class' => 'ri-text-line',
        ]);

        $field->amenities()->attach($amenity->id);

        $booking = Reservation::factory()->create([
            'field_id' => $field->id,
            'user_id' => $this->client->id,
        ]);

        $response = $this->actingAs($this->client)
            ->get(route('bookings.show', $booking));

        $response->assertStatus(200);
        $response->assertDontSee(substr($longName, 0, 50)); // Amenities not displayed (feature removed)
        $response->assertDontSee('TypeError');
    }

    #[Test]
    public function amenity_with_html_in_name_is_escaped()
    {
        $field = Field::factory()->create();
        $amenity = Amenity::factory()->create([
            'name' => '<script>alert("xss")</script>Amenity',
            'icon_class' => 'ri-shield-line',
        ]);

        $field->amenities()->attach($amenity->id);

        $booking = Reservation::factory()->create([
            'field_id' => $field->id,
            'user_id' => $this->client->id,
        ]);

        $response = $this->actingAs($this->client)
            ->get(route('bookings.show', $booking));

        $response->assertStatus(200);

        // Amenities not displayed (feature removed), but should not execute HTML
        $content = $response->getContent();
        $this->assertStringNotContainsString('&lt;script&gt;', $content); // Not displayed
        $this->assertStringNotContainsString('<script>alert', $content);
    }

    #[Test]
    public function amenity_with_empty_icon_class_uses_fallback()
    {
        $field = Field::factory()->create();
        $amenity = Amenity::factory()->create([
            'name' => 'No Icon Amenity',
            'icon_class' => '', // Empty string instead of null
        ]);

        $field->amenities()->attach($amenity->id);

        $booking = Reservation::factory()->create([
            'field_id' => $field->id,
            'user_id' => $this->client->id,
        ]);

        $response = $this->actingAs($this->client)
            ->get(route('bookings.show', $booking));

        $response->assertStatus(200);
        $response->assertDontSee('No Icon Amenity'); // Amenities not displayed (feature removed)
        // Empty string icon_class will be displayed as-is, not as fallback
        $response->assertDontSee('TypeError');
    }

    #[Test]
    public function amenity_with_whitespace_icon_class_uses_fallback()
    {
        $field = Field::factory()->create();
        $amenity = Amenity::factory()->create([
            'name' => 'Whitespace Icon Amenity',
            'icon_class' => '   ', // Whitespace only
        ]);

        $field->amenities()->attach($amenity->id);

        $booking = Reservation::factory()->create([
            'field_id' => $field->id,
            'user_id' => $this->client->id,
        ]);

        $response = $this->actingAs($this->client)
            ->get(route('bookings.show', $booking));

        $response->assertStatus(200);
        $response->assertDontSee('Whitespace Icon Amenity'); // Amenities not displayed (feature removed)
        // The template should handle whitespace and use fallback
        $response->assertDontSee('TypeError');
    }

    #[Test]
    public function field_with_soft_deleted_amenities_handled_correctly()
    {
        $field = Field::factory()->create();
        $activeAmenity = Amenity::factory()->create(['name' => 'Active Amenity']);
        $deletedAmenity = Amenity::factory()->create(['name' => 'Deleted Amenity']);

        $field->amenities()->attach([$activeAmenity->id, $deletedAmenity->id]);

        // Soft delete one amenity
        $deletedAmenity->delete();

        $booking = Reservation::factory()->create([
            'field_id' => $field->id,
            'user_id' => $this->client->id,
        ]);

        $response = $this->actingAs($this->client)
            ->get(route('bookings.show', $booking));

        $response->assertStatus(200);
        $response->assertDontSee('Active Amenity'); // Amenities not displayed (feature removed)
        $response->assertDontSee('Deleted Amenity'); // Should not show soft deleted
        $response->assertDontSee('TypeError');
    }

    #[Test]
    public function concurrent_amenity_access_does_not_cause_issues()
    {
        $field = Field::factory()->create();
        $amenity = Amenity::factory()->create(['name' => 'Concurrent Test']);
        $field->amenities()->attach($amenity->id);

        $booking = Reservation::factory()->create([
            'field_id' => $field->id,
            'user_id' => $this->client->id,
        ]);

        // Simulate multiple concurrent requests
        $responses = [];
        for ($i = 0; $i < 5; $i++) {
            $responses[] = $this->actingAs($this->client)
                ->get(route('bookings.show', $booking));
        }

        foreach ($responses as $response) {
            $response->assertStatus(200);
            $response->assertDontSee('Concurrent Test'); // Amenities not displayed (feature removed)
            $response->assertDontSee('TypeError');
        }
    }

    #[Test]
    public function amenity_relationship_data_persists_correctly()
    {
        $field = Field::factory()->create();
        $amenity = Amenity::factory()->create(['name' => 'Persistence Test']);
        $field->amenities()->attach($amenity->id);

        // Verify data persists when models are refreshed
        $field->refresh();
        $amenity->refresh();

        $this->assertEquals(1, $field->amenities->count());
        $this->assertEquals('Persistence Test', $field->amenities->first()->name);
        $this->assertTrue($amenity->fields->contains($field));
    }

    #[Test]
    public function amenity_with_unicode_characters_handled_correctly()
    {
        $field = Field::factory()->create();
        $amenity = Amenity::factory()->create([
            'name' => '🏟️ Stadium Amenity 🎯',
            'icon_class' => 'ri-football-line',
        ]);

        $field->amenities()->attach($amenity->id);

        $booking = Reservation::factory()->create([
            'field_id' => $field->id,
            'user_id' => $this->client->id,
        ]);

        $response = $this->actingAs($this->client)
            ->get(route('bookings.show', $booking));

        $response->assertStatus(200);
        $response->assertDontSee('🏟️ Stadium Amenity 🎯'); // Amenities not displayed (feature removed)
        $response->assertDontSee('TypeError');
    }

    #[Test]
    public function get_available_amenities_returns_expected_structure()
    {
        // Create some amenities first
        $amenity1 = Amenity::factory()->create(['name' => 'Test Amenity 1', 'is_active' => true]);
        $amenity2 = Amenity::factory()->create(['name' => 'Test Amenity 2', 'is_active' => true]);
        $amenity3 = Amenity::factory()->create(['name' => 'Inactive Amenity', 'is_active' => false]);

        $availableAmenities = Field::getAvailableAmenities();

        // Should return an array with ID keys and name values
        $this->assertIsArray($availableAmenities);
        $this->assertArrayHasKey($amenity1->id, $availableAmenities);
        $this->assertArrayHasKey($amenity2->id, $availableAmenities);
        $this->assertArrayNotHasKey($amenity3->id, $availableAmenities); // Inactive should not be included

        $this->assertEquals('Test Amenity 1', $availableAmenities[$amenity1->id]);
        $this->assertEquals('Test Amenity 2', $availableAmenities[$amenity2->id]);
    }

    #[Test]
    public function amenity_collection_iteration_handles_modified_collection()
    {
        $field = Field::factory()->create();
        $amenities = Amenity::factory()->count(3)->create();
        $field->amenities()->attach($amenities);

        $booking = Reservation::factory()->create([
            'field_id' => $field->id,
            'user_id' => $this->client->id,
        ]);

        // Modify amenities while potentially being accessed
        $amenities[0]->update(['name' => 'Modified During Access']);

        $response = $this->actingAs($this->client)
            ->get(route('bookings.show', $booking));

        $response->assertStatus(200);
        $response->assertDontSee('TypeError');
    }

    #[Test]
    public function field_amenities_count_check_handles_null_relationship()
    {
        // Create a field but don't load the amenities relationship
        $field = Field::factory()->create();

        // This should not cause errors even if relationship is not loaded
        $booking = Reservation::factory()->create([
            'field_id' => $field->id,
            'user_id' => $this->client->id,
        ]);

        $response = $this->actingAs($this->client)
            ->get(route('bookings.show', $booking));

        $response->assertStatus(200);
        $response->assertDontSee('Amenities:'); // No amenities section
        $response->assertDontSee('TypeError');
    }

    #[Test]
    public function amenity_object_string_conversion_does_not_break_templates()
    {
        $field = Field::factory()->create();
        $amenity = Amenity::factory()->create(['name' => 'String Conversion Test']);
        $field->amenities()->attach($amenity->id);

        // Test that accidentally trying to use amenity as string doesn't break
        $amenityAsString = (string) $amenity; // This should work without errors
        $this->assertStringContainsString('String Conversion Test', $amenityAsString);

        $booking = Reservation::factory()->create([
            'field_id' => $field->id,
            'user_id' => $this->client->id,
        ]);

        $response = $this->actingAs($this->client)
            ->get(route('bookings.show', $booking));

        $response->assertStatus(200);
        $response->assertDontSee('String Conversion Test'); // Amenities not displayed (feature removed)
        $response->assertDontSee('TypeError');
    }

    #[Test]
    public function amenity_relationship_handles_soft_deleted_amenities()
    {
        $field = Field::factory()->create();
        $amenity = Amenity::factory()->create(['name' => 'Soft Delete Test']);
        $field->amenities()->attach($amenity->id);

        // Verify amenity is attached
        $this->assertEquals(1, $field->amenities->count());
        $this->assertTrue($field->amenities->contains($amenity));

        // Soft delete the amenity
        $amenity->delete();

        // Refresh field to get updated relationship
        $field->refresh();

        // Should not include soft deleted amenities by default
        $this->assertEquals(0, $field->amenities->count());

        // But should include them when using withTrashed
        $this->assertEquals(1, $field->amenities()->withTrashed()->count());

        $booking = Reservation::factory()->create([
            'field_id' => $field->id,
            'user_id' => $this->client->id,
        ]);

        // Should handle soft deleted amenities gracefully
        $response = $this->actingAs($this->client)
            ->get(route('bookings.show', $booking));

        $response->assertStatus(200);
        $response->assertDontSee('TypeError');
        $response->assertDontSee('Soft Delete Test'); // Should not show soft deleted amenity
    }
}
