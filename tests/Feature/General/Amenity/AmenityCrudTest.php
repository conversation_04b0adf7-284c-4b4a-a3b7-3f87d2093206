<?php

namespace Tests\Feature;

use App\Models\Amenity;
use App\Models\Field;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(\App\Http\Controllers\Admin\AmenityController::class)]
class AmenityCrudTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $admin;

    protected $employee;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test users
        $this->admin = User::factory()->create(['role' => 'admin']);
        $this->employee = User::factory()->create(['role' => 'employee']);
    }

    #[Test]
    public function admin_can_view_amenities_index()
    {
        // Create some test amenities
        Amenity::factory()->count(5)->create();

        $response = $this->actingAs($this->admin)
            ->get(route('admin.amenities.index'));

        $response->assertStatus(200);
        $response->assertSee('Amenity Management');
        $response->assertSee('Add New Amenity');

        echo "✅ Admin can view amenities index\n";
    }

    #[Test]
    public function non_admin_cannot_access_amenities()
    {
        $response = $this->actingAs($this->employee)
            ->get(route('admin.amenities.index'));

        $response->assertStatus(403);

        echo "✅ Non-admin users are properly restricted\n";
    }

    #[Test]
    public function admin_can_view_create_amenity_form()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.amenities.create'));

        $response->assertStatus(200);
        $response->assertSee('Create New Amenity');
        $response->assertSee('Amenity Name');
        $response->assertSee('Icon Class');

        echo "✅ Admin can view create amenity form\n";
    }

    #[Test]
    public function admin_can_create_amenity_successfully()
    {
        $amenityData = [
            'name' => 'Test Amenity',
            'description' => 'This is a test amenity description',
            'icon_class' => 'ri-test-line',
            'is_active' => true,
        ];

        $response = $this->actingAs($this->admin)
            ->post(route('admin.amenities.store'), $amenityData);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('amenities', [
            'name' => 'Test Amenity',
            'description' => 'This is a test amenity description',
            'icon_class' => 'ri-test-line',
            'is_active' => true,
        ]);

        echo "✅ Admin can create amenity successfully\n";
    }

    #[Test]
    public function amenity_creation_requires_valid_data()
    {
        // Test missing name
        $response = $this->actingAs($this->admin)
            ->post(route('admin.amenities.store'), [
                'description' => 'Test description',
                'icon_class' => 'ri-test-line',
            ]);

        $response->assertSessionHasErrors(['name']);

        // Test duplicate name
        Amenity::factory()->create(['name' => 'Existing Amenity']);

        $response = $this->actingAs($this->admin)
            ->post(route('admin.amenities.store'), [
                'name' => 'Existing Amenity',
                'icon_class' => 'ri-test-line',
            ]);

        $response->assertSessionHasErrors(['name']);

        echo "✅ Amenity creation validation works correctly\n";
    }

    #[Test]
    public function admin_can_view_amenity_details()
    {
        $amenity = Amenity::factory()->create([
            'name' => 'Test Amenity',
            'description' => 'Test description',
        ]);

        $response = $this->actingAs($this->admin)
            ->get(route('admin.amenities.show', $amenity));

        $response->assertStatus(200);
        $response->assertSee('Test Amenity');
        $response->assertSee('Test description');
        $response->assertSee('Edit Amenity');

        echo "✅ Admin can view amenity details\n";
    }

    #[Test]
    public function admin_can_edit_amenity()
    {
        $amenity = Amenity::factory()->create();

        $response = $this->actingAs($this->admin)
            ->get(route('admin.amenities.edit', $amenity));

        $response->assertStatus(200);
        $response->assertSee('Edit Amenity');
        $response->assertSee($amenity->name);

        echo "✅ Admin can view edit amenity form\n";
    }

    #[Test]
    public function admin_can_update_amenity_successfully()
    {
        $amenity = Amenity::factory()->create([
            'name' => 'Original Name',
            'description' => 'Original description',
        ]);

        $updateData = [
            'name' => 'Updated Name',
            'description' => 'Updated description',
            'icon_class' => 'ri-updated-line',
            'is_active' => false,
        ];

        $response = $this->actingAs($this->admin)
            ->put(route('admin.amenities.update', $amenity), $updateData);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('amenities', [
            'id' => $amenity->id,
            'name' => 'Updated Name',
            'description' => 'Updated description',
            'icon_class' => 'ri-updated-line',
            'is_active' => false,
        ]);

        echo "✅ Admin can update amenity successfully\n";
    }

    #[Test]
    public function admin_can_delete_unused_amenity()
    {
        $amenity = Amenity::factory()->create();

        $response = $this->actingAs($this->admin)
            ->delete(route('admin.amenities.destroy', $amenity));

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->assertSoftDeleted('amenities', ['id' => $amenity->id]);

        echo "✅ Admin can delete unused amenity\n";
    }

    #[Test]
    public function admin_cannot_delete_amenity_in_use()
    {
        $amenity = Amenity::factory()->create();
        $field = Field::factory()->create();
        $field->amenities()->attach($amenity);

        $response = $this->actingAs($this->admin)
            ->delete(route('admin.amenities.destroy', $amenity));

        $response->assertRedirect();
        $response->assertSessionHas('error');

        $this->assertDatabaseHas('amenities', ['id' => $amenity->id]);

        echo "✅ Admin cannot delete amenity in use\n";
    }
}
