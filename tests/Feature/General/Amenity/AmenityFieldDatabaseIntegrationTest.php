<?php

namespace Tests\Feature;

use App\Models\Amenity;
use App\Models\Field;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class AmenityFieldDatabaseIntegrationTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function field_amenity_pivot_table_structure_is_correct()
    {
        // Verify the pivot table exists and has correct structure
        $this->assertTrue(DB::getSchemaBuilder()->hasTable('field_amenity'));

        $columns = DB::getSchemaBuilder()->getColumnListing('field_amenity');
        $this->assertContains('id', $columns);
        $this->assertContains('field_id', $columns);
        $this->assertContains('amenity_id', $columns);
        $this->assertContains('created_at', $columns);
        $this->assertContains('updated_at', $columns);
    }

    #[Test]
    public function amenities_table_structure_supports_required_fields()
    {
        $columns = DB::getSchemaBuilder()->getColumnListing('amenities');

        $this->assertContains('id', $columns);
        $this->assertContains('name', $columns);
        $this->assertContains('description', $columns);
        $this->assertContains('icon_class', $columns);
        $this->assertContains('is_active', $columns);
        $this->assertContains('created_at', $columns);
        $this->assertContains('updated_at', $columns);
        $this->assertContains('deleted_at', $columns); // Soft deletes
    }

    #[Test]
    public function field_amenity_relationship_creates_correct_database_records()
    {
        $field = Field::factory()->create();
        $amenity1 = Amenity::factory()->create(['name' => 'DB Test 1']);
        $amenity2 = Amenity::factory()->create(['name' => 'DB Test 2']);

        // Attach amenities
        $field->amenities()->attach([$amenity1->id, $amenity2->id]);

        // Verify pivot records exist
        $this->assertDatabaseHas('field_amenity', [
            'field_id' => $field->id,
            'amenity_id' => $amenity1->id,
        ]);

        $this->assertDatabaseHas('field_amenity', [
            'field_id' => $field->id,
            'amenity_id' => $amenity2->id,
        ]);

        // Verify count
        $pivotCount = DB::table('field_amenity')
            ->where('field_id', $field->id)
            ->count();
        $this->assertEquals(2, $pivotCount);
    }

    #[Test]
    public function amenity_factory_creates_valid_database_records()
    {
        $amenity = Amenity::factory()->create([
            'name' => 'Factory Test',
            'description' => 'Factory Description',
            'icon_class' => 'ri-factory-line',
            'is_active' => true,
        ]);

        // Verify database record
        $this->assertDatabaseHas('amenities', [
            'id' => $amenity->id,
            'name' => 'Factory Test',
            'description' => 'Factory Description',
            'icon_class' => 'ri-factory-line',
            'is_active' => true,
        ]);

        // Verify object properties match database
        $dbAmenity = Amenity::find($amenity->id);
        $this->assertEquals('Factory Test', $dbAmenity->name);
        $this->assertEquals('Factory Description', $dbAmenity->description);
        $this->assertEquals('ri-factory-line', $dbAmenity->icon_class);
        $this->assertTrue($dbAmenity->is_active);
    }

    #[Test]
    public function field_factory_creates_valid_database_records()
    {
        $field = Field::factory()->create([
            'name' => 'DB Field Test',
            'type' => 'Soccer',
            'hourly_rate' => 75.50,
            'capacity' => 22,
            'status' => 'Active',
        ]);

        // Verify database record
        $this->assertDatabaseHas('fields', [
            'id' => $field->id,
            'name' => 'DB Field Test',
            'type' => 'Soccer',
            'hourly_rate' => '75.50',
            'capacity' => 22,
            'status' => 'Active',
        ]);
    }

    #[Test]
    public function many_to_many_relationship_works_bidirectionally()
    {
        $field1 = Field::factory()->create(['name' => 'Field 1']);
        $field2 = Field::factory()->create(['name' => 'Field 2']);
        $amenity = Amenity::factory()->create(['name' => 'Shared Amenity']);

        // Attach amenity to both fields
        $field1->amenities()->attach($amenity->id);
        $field2->amenities()->attach($amenity->id);

        // Test field -> amenities relationship
        $this->assertTrue($field1->amenities->contains($amenity));
        $this->assertTrue($field2->amenities->contains($amenity));

        // Test amenity -> fields relationship
        $this->assertTrue($amenity->fields->contains($field1));
        $this->assertTrue($amenity->fields->contains($field2));
        $this->assertEquals(2, $amenity->fields->count());

        // Verify database integrity
        $pivotRecords = DB::table('field_amenity')
            ->where('amenity_id', $amenity->id)
            ->count();
        $this->assertEquals(2, $pivotRecords);
    }

    #[Test]
    public function cascade_delete_works_correctly()
    {
        $field = Field::factory()->create();
        $amenity = Amenity::factory()->create();

        $field->amenities()->attach($amenity->id);

        // Verify pivot record exists
        $this->assertDatabaseHas('field_amenity', [
            'field_id' => $field->id,
            'amenity_id' => $amenity->id,
        ]);

        // Delete field (hard delete for testing)
        $field->forceDelete();

        // Pivot record should be deleted due to cascade
        $this->assertDatabaseMissing('field_amenity', [
            'field_id' => $field->id,
            'amenity_id' => $amenity->id,
        ]);

        // Amenity should still exist
        $this->assertDatabaseHas('amenities', ['id' => $amenity->id]);
    }

    #[Test]
    public function soft_delete_preserves_relationships()
    {
        $field = Field::factory()->create();
        $amenity = Amenity::factory()->create();

        $field->amenities()->attach($amenity->id);

        // Soft delete amenity
        $amenity->delete();

        // Pivot record should still exist
        $this->assertDatabaseHas('field_amenity', [
            'field_id' => $field->id,
            'amenity_id' => $amenity->id,
        ]);

        // But amenity should be soft deleted
        $this->assertSoftDeleted('amenities', ['id' => $amenity->id]);

        // Field should not see soft deleted amenity by default
        $field->refresh();
        $this->assertEquals(0, $field->amenities->count());

        // But should see it with trashed
        $this->assertEquals(1, $field->amenities()->withTrashed()->count());
    }

    #[Test]
    public function duplicate_field_amenity_attachment_is_prevented()
    {
        $field = Field::factory()->create();
        $amenity = Amenity::factory()->create();

        // First attachment should work
        $field->amenities()->attach($amenity->id);
        $this->assertEquals(1, $field->amenities->count());

        // Second attachment of same combination should be prevented by unique constraint
        $this->expectException(\Illuminate\Database\UniqueConstraintViolationException::class);
        $field->amenities()->attach($amenity->id);
    }

    #[Test]
    public function sync_method_works_correctly_with_database()
    {
        $field = Field::factory()->create();
        $amenity1 = Amenity::factory()->create(['name' => 'Sync 1']);
        $amenity2 = Amenity::factory()->create(['name' => 'Sync 2']);
        $amenity3 = Amenity::factory()->create(['name' => 'Sync 3']);

        // Initial sync
        $field->amenities()->sync([$amenity1->id, $amenity2->id]);

        $this->assertEquals(2, $field->amenities->count());
        $this->assertDatabaseHas('field_amenity', ['field_id' => $field->id, 'amenity_id' => $amenity1->id]);
        $this->assertDatabaseHas('field_amenity', ['field_id' => $field->id, 'amenity_id' => $amenity2->id]);

        // Sync with different amenities
        $field->amenities()->sync([$amenity2->id, $amenity3->id]);

        $field->refresh();
        $this->assertEquals(2, $field->amenities->count());
        $this->assertDatabaseMissing('field_amenity', ['field_id' => $field->id, 'amenity_id' => $amenity1->id]);
        $this->assertDatabaseHas('field_amenity', ['field_id' => $field->id, 'amenity_id' => $amenity2->id]);
        $this->assertDatabaseHas('field_amenity', ['field_id' => $field->id, 'amenity_id' => $amenity3->id]);
    }

    #[Test]
    public function eager_loading_prevents_n_plus_one_queries()
    {
        // Create fields with amenities
        $fields = Field::factory()->count(3)->create();
        $amenities = Amenity::factory()->count(5)->create();

        foreach ($fields as $field) {
            $field->amenities()->attach($amenities->random(2)->pluck('id'));
        }

        // Clear any existing query log
        DB::flushQueryLog();

        // Test without eager loading (should have many queries)
        DB::enableQueryLog();
        $fieldsWithoutEager = Field::all();
        foreach ($fieldsWithoutEager as $field) {
            $amenityCount = $field->amenities->count(); // This triggers additional queries
        }
        $queriesWithoutEager = count(DB::getQueryLog());
        DB::flushQueryLog();

        // Test with eager loading (should have fewer queries)
        DB::enableQueryLog();
        $fieldsWithEager = Field::with('amenities')->get();
        foreach ($fieldsWithEager as $field) {
            $amenityCount = $field->amenities->count(); // This should not trigger additional queries
        }
        $queriesWithEager = count(DB::getQueryLog());
        DB::disableQueryLog();

        // Eager loading should use fewer queries
        // Without eager loading: 1 query for fields + 3 queries for amenities = 4 queries
        // With eager loading: 1 query for fields + 1 query for all amenities = 2 queries
        $this->assertLessThan($queriesWithoutEager, $queriesWithEager);
        $this->assertGreaterThanOrEqual(2, $queriesWithEager); // At least 2 queries (fields + amenities)
        $this->assertGreaterThanOrEqual(4, $queriesWithoutEager); // At least 4 queries (N+1 problem)
    }

    #[Test]
    public function database_queries_perform_reasonably_well()
    {
        // Test that queries using the relationship are reasonably fast
        $field = Field::factory()->create();
        $amenities = Amenity::factory()->count(50)->create(); // Reduced count for faster tests
        $field->amenities()->attach($amenities->pluck('id'));

        $startTime = microtime(true);
        $count = $field->amenities->count();
        $endTime = microtime(true);

        $this->assertEquals(50, $count);
        $this->assertLessThan(2.0, $endTime - $startTime); // Should complete in under 2 seconds

        // Test that we can query amenities efficiently
        $startTime = microtime(true);
        $activeAmenities = $field->amenities()->where('is_active', true)->get();
        $endTime = microtime(true);

        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Collection::class, $activeAmenities);
        $this->assertLessThan(2.0, $endTime - $startTime); // Should complete in under 2 seconds
    }

    #[Test]
    public function amenity_name_uniqueness_constraint_works()
    {
        Amenity::factory()->create(['name' => 'Unique Name']);

        $this->expectException(\Illuminate\Database\QueryException::class);
        Amenity::factory()->create(['name' => 'Unique Name']); // Should fail
    }

    #[Test]
    public function field_amenity_pivot_record_is_created_correctly()
    {
        $field = Field::factory()->create();
        $amenity = Amenity::factory()->create();

        // Attach amenity to field
        $field->amenities()->attach($amenity->id);

        // Verify pivot record exists
        $pivotRecord = DB::table('field_amenity')
            ->where('field_id', $field->id)
            ->where('amenity_id', $amenity->id)
            ->first();

        $this->assertNotNull($pivotRecord);
        $this->assertEquals($field->id, $pivotRecord->field_id);
        $this->assertEquals($amenity->id, $pivotRecord->amenity_id);

        // Verify the relationship works from both sides
        $this->assertTrue($field->amenities->contains($amenity));
        $this->assertTrue($amenity->fields->contains($field));
    }
}
