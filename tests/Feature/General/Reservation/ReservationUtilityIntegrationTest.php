<?php

namespace Tests\Feature;

use App\Models\Field;
use App\Models\Reservation;
use App\Models\User;
use App\Models\Utility;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(\App\Http\Controllers\ReservationController::class)]
class ReservationUtilityIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected Field $field;

    protected Utility $utility1;

    protected Utility $utility2;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user
        $this->user = User::factory()->create([
            'role' => 'employee',
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);

        // Create test field
        $this->field = Field::factory()->create([
            'name' => 'Test Soccer Field',
            'type' => 'Soccer',
            'hourly_rate' => 50.00,
            'capacity' => 22,
            'status' => 'Active',
            'opening_time' => '08:00',
            'closing_time' => '22:00',
            'min_booking_hours' => 1,
            'max_booking_hours' => 8,
        ]);

        // Create test utilities
        $this->utility1 = Utility::factory()->create([
            'name' => 'Sound System',
            'description' => 'Professional sound system',
            'hourly_rate' => 25.00,
            'is_active' => true,
        ]);

        $this->utility2 = Utility::factory()->create([
            'name' => 'Lighting Equipment',
            'description' => 'Professional lighting setup',
            'hourly_rate' => 30.00,
            'is_active' => true,
        ]);
    }

    #[Test]
    public function user_can_create_reservation_with_single_utility()
    {
        $reservationData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(1)->format('Y-m-d'),
            'start_time' => '10:00',
            'duration_hours' => 2,
            'customer_name' => 'John Doe',
            'customer_email' => '<EMAIL>',
            'utilities' => [
                [
                    'id' => $this->utility1->id,
                    'hours' => 2,
                ],
            ],
        ];

        $response = $this->actingAs($this->user)
            ->post(route('reservations.store'), $reservationData);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify reservation was created
        $reservation = Reservation::where('user_id', $this->user->id)
            ->where('field_id', $this->field->id)
            ->first();

        $this->assertNotNull($reservation);
        $this->assertEquals('Confirmed', $reservation->status);

        // Verify utility was attached with correct pivot data
        $this->assertEquals(1, $reservation->utilities->count());
        $attachedUtility = $reservation->utilities->first();
        $this->assertEquals($this->utility1->id, $attachedUtility->id);
        $this->assertEquals(2, $attachedUtility->pivot->hours);
        $this->assertEquals(25.00, $attachedUtility->pivot->rate);
        $this->assertEquals(50.00, $attachedUtility->pivot->cost); // 25.00 * 2 hours
    }

    #[Test]
    public function user_can_create_reservation_with_multiple_utilities()
    {
        $reservationData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(1)->format('Y-m-d'),
            'start_time' => '14:00',
            'duration_hours' => 3,
            'customer_name' => 'Jane Smith',
            'utilities' => [
                [
                    'id' => $this->utility1->id,
                    'hours' => 3,
                ],
                [
                    'id' => $this->utility2->id,
                    'hours' => 2,
                ],
            ],
        ];

        $response = $this->actingAs($this->user)
            ->post(route('reservations.store'), $reservationData);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify reservation was created
        $reservation = Reservation::where('user_id', $this->user->id)
            ->where('customer_name', 'Jane Smith')
            ->first();

        $this->assertNotNull($reservation);

        // Verify both utilities were attached
        $this->assertEquals(2, $reservation->utilities->count());

        // Verify utility 1 data
        $utility1Data = $reservation->utilities->where('id', $this->utility1->id)->first();
        $this->assertNotNull($utility1Data);
        $this->assertEquals(3, $utility1Data->pivot->hours);
        $this->assertEquals(25.00, $utility1Data->pivot->rate);
        $this->assertEquals(75.00, $utility1Data->pivot->cost); // 25.00 * 3 hours

        // Verify utility 2 data
        $utility2Data = $reservation->utilities->where('id', $this->utility2->id)->first();
        $this->assertNotNull($utility2Data);
        $this->assertEquals(2, $utility2Data->pivot->hours);
        $this->assertEquals(30.00, $utility2Data->pivot->rate);
        $this->assertEquals(60.00, $utility2Data->pivot->cost); // 30.00 * 2 hours
    }

    #[Test]
    public function reservation_creation_validates_utility_exists()
    {
        $reservationData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(1)->format('Y-m-d'),
            'start_time' => '10:00',
            'duration_hours' => 2,
            'utilities' => [
                [
                    'id' => 999, // Non-existent utility
                    'hours' => 2,
                ],
            ],
        ];

        $response = $this->actingAs($this->user)
            ->post(route('reservations.store'), $reservationData);

        $response->assertSessionHasErrors(['utilities.0.id']);
    }

    #[Test]
    public function reservation_creation_validates_utility_hours()
    {
        $reservationData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(1)->format('Y-m-d'),
            'start_time' => '10:00',
            'duration_hours' => 2,
            'utilities' => [
                [
                    'id' => $this->utility1->id,
                    'hours' => 0, // Invalid hours
                ],
            ],
        ];

        $response = $this->actingAs($this->user)
            ->post(route('reservations.store'), $reservationData);

        $response->assertSessionHasErrors(['utilities.0.hours']);
    }

    #[Test]
    public function user_can_create_reservation_without_utilities()
    {
        $reservationData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(1)->format('Y-m-d'),
            'start_time' => '10:00',
            'duration_hours' => 2,
            'customer_name' => 'No Utilities Customer',
            // No utilities array
        ];

        $response = $this->actingAs($this->user)
            ->post(route('reservations.store'), $reservationData);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify reservation was created without utilities
        $reservation = Reservation::where('customer_name', 'No Utilities Customer')->first();
        $this->assertNotNull($reservation);
        $this->assertEquals(0, $reservation->utilities->count());
    }

    #[Test]
    public function user_can_update_reservation_utilities()
    {
        // Create initial reservation with one utility
        $reservation = Reservation::factory()->create([
            'user_id' => $this->user->id,
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(2)->format('Y-m-d'),
            'start_time' => '10:00',
            'end_time' => '12:00',
            'duration_hours' => 2,
            'status' => 'Confirmed',
        ]);

        $reservation->utilities()->attach($this->utility1->id, [
            'hours' => 2,
            'rate' => 25.00,
            'cost' => 50.00,
        ]);

        // Update to use different utility
        $updateData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(2)->format('Y-m-d'),
            'start_time' => '10:00',
            'duration_hours' => 2,
            'utilities' => [
                [
                    'id' => $this->utility2->id,
                    'hours' => 2,
                ],
            ],
        ];

        $response = $this->actingAs($this->user)
            ->put(route('reservations.update', $reservation), $updateData);

        $response->assertRedirect(route('reservations.show', $reservation));
        $response->assertSessionHas('success');

        // Verify utilities were updated
        $reservation->refresh();
        $this->assertEquals(1, $reservation->utilities->count());
        $this->assertEquals($this->utility2->id, $reservation->utilities->first()->id);
    }

    #[Test]
    public function inactive_utilities_are_not_available_for_new_reservations()
    {
        // Create inactive utility
        $inactiveUtility = Utility::factory()->create([
            'name' => 'Inactive Utility',
            'hourly_rate' => 20.00,
            'is_active' => false,
        ]);

        $reservationData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(1)->format('Y-m-d'),
            'start_time' => '10:00',
            'duration_hours' => 2,
            'utilities' => [
                [
                    'id' => $inactiveUtility->id,
                    'hours' => 2,
                ],
            ],
        ];

        $response = $this->actingAs($this->user)
            ->post(route('reservations.store'), $reservationData);

        // Should fail validation since inactive utilities shouldn't be selectable
        $response->assertSessionHasErrors(['utilities.0.id']);
    }

    #[Test]
    public function reservation_creation_form_shows_active_utilities()
    {
        // Create mix of active and inactive utilities
        $activeUtility = Utility::factory()->create(['is_active' => true, 'name' => 'Active Utility']);
        $inactiveUtility = Utility::factory()->create(['is_active' => false, 'name' => 'Inactive Utility']);

        $response = $this->actingAs($this->user)
            ->get(route('reservations.create'));

        $response->assertStatus(200);
        $response->assertSee('Active Utility');
        $response->assertDontSee('Inactive Utility');
    }

    #[Test]
    public function reservation_show_page_displays_attached_utilities()
    {
        $reservation = Reservation::factory()->create([
            'user_id' => $this->user->id,
            'field_id' => $this->field->id,
        ]);

        $reservation->utilities()->attach([
            $this->utility1->id => ['hours' => 2, 'rate' => 25.00, 'cost' => 50.00],
            $this->utility2->id => ['hours' => 1, 'rate' => 30.00, 'cost' => 30.00],
        ]);

        $response = $this->actingAs($this->user)
            ->get(route('reservations.show', $reservation));

        $response->assertStatus(200);
        $response->assertSee($this->utility1->name);
        $response->assertSee($this->utility2->name);
        $response->assertSee('XCG 50.00'); // utility1 cost
        $response->assertSee('XCG 30.00'); // utility2 cost
    }

    #[Test]
    public function utility_cost_calculation_is_accurate()
    {
        $reservationData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(1)->format('Y-m-d'),
            'start_time' => '10:00',
            'duration_hours' => 2,
            'utilities' => [
                [
                    'id' => $this->utility1->id,
                    'hours' => 3, // Different from reservation duration
                ],
                [
                    'id' => $this->utility2->id,
                    'hours' => 1,
                ],
            ],
        ];

        $response = $this->actingAs($this->user)
            ->post(route('reservations.store'), $reservationData);

        $response->assertRedirect();

        $reservation = Reservation::where('user_id', $this->user->id)->latest()->first();

        // Verify total cost includes field + utility costs
        $expectedFieldCost = $this->field->hourly_rate * 2; // 50.00 * 2 = 100.00
        $expectedUtilityCost = (25.00 * 3) + (30.00 * 1); // 75.00 + 30.00 = 105.00
        $expectedTotalCost = $expectedFieldCost + $expectedUtilityCost; // 100.00 + 105.00 = 205.00
        $this->assertEquals($expectedTotalCost, $reservation->total_cost);

        // Verify utility costs are calculated separately
        $utility1Cost = $reservation->utilities->where('id', $this->utility1->id)->first()->pivot->cost;
        $utility2Cost = $reservation->utilities->where('id', $this->utility2->id)->first()->pivot->cost;

        $this->assertEquals(75.00, $utility1Cost); // 25.00 * 3 hours
        $this->assertEquals(30.00, $utility2Cost); // 30.00 * 1 hour
    }

    #[Test]
    public function user_can_remove_all_utilities_from_reservation()
    {
        // Create reservation with utilities
        $reservation = Reservation::factory()->create([
            'user_id' => $this->user->id,
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(2)->format('Y-m-d'),
            'start_time' => '10:00',
            'end_time' => '12:00',
            'duration_hours' => 2,
            'status' => 'Confirmed',
        ]);

        $reservation->utilities()->attach([
            $this->utility1->id => ['hours' => 2, 'rate' => 25.00, 'cost' => 50.00],
            $this->utility2->id => ['hours' => 2, 'rate' => 30.00, 'cost' => 60.00],
        ]);

        $this->assertEquals(2, $reservation->utilities->count());

        // Update reservation to remove all utilities
        $updateData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(2)->format('Y-m-d'),
            'start_time' => '10:00',
            'duration_hours' => 2,
            // No utilities array = remove all utilities
        ];

        $response = $this->actingAs($this->user)
            ->put(route('reservations.update', $reservation), $updateData);

        $response->assertRedirect();

        // Verify all utilities were removed
        $reservation->refresh();
        $this->assertEquals(0, $reservation->utilities->count());
    }

    #[Test]
    public function deleting_utility_removes_it_from_existing_reservations()
    {
        // Create reservation with utility
        $reservation = Reservation::factory()->create([
            'user_id' => $this->user->id,
            'field_id' => $this->field->id,
        ]);

        $reservation->utilities()->attach($this->utility1->id, [
            'hours' => 2,
            'rate' => 25.00,
            'cost' => 50.00,
        ]);

        $this->assertEquals(1, $reservation->utilities->count());

        // Soft delete the utility
        $this->utility1->delete();

        // Verify the utility is removed from the reservation due to cascade delete
        $reservation->refresh();
        $this->assertEquals(0, $reservation->utilities->count());
    }

    #[Test]
    public function reservation_edit_form_shows_current_utilities()
    {
        $reservation = Reservation::factory()->create([
            'user_id' => $this->user->id,
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(2)->format('Y-m-d'),
            'status' => 'Confirmed',
        ]);

        $reservation->utilities()->attach($this->utility1->id, [
            'hours' => 2,
            'rate' => 25.00,
            'cost' => 50.00,
        ]);

        $response = $this->actingAs($this->user)
            ->get(route('reservations.edit', $reservation));

        $response->assertStatus(200);
        $response->assertSee($this->utility1->name);
        // Should show the utility as selected/checked
    }

    #[Test]
    public function utility_hours_can_be_different_from_reservation_duration()
    {
        $reservationData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(1)->format('Y-m-d'),
            'start_time' => '10:00',
            'duration_hours' => 4, // Reservation is 4 hours
            'utilities' => [
                [
                    'id' => $this->utility1->id,
                    'hours' => 2, // But utility only needed for 2 hours
                ],
            ],
        ];

        $response = $this->actingAs($this->user)
            ->post(route('reservations.store'), $reservationData);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $reservation = Reservation::where('user_id', $this->user->id)->latest()->first();

        // Verify reservation duration
        $this->assertEquals(4, $reservation->duration_hours);

        // Verify utility hours are independent
        $utilityData = $reservation->utilities->first();
        $this->assertEquals(2, $utilityData->pivot->hours);
        $this->assertEquals(50.00, $utilityData->pivot->cost); // 25.00 * 2 hours
    }

    #[Test]
    public function complex_reservation_with_multiple_utilities_and_different_hours()
    {
        // Create additional utilities for complex scenario
        $utility3 = Utility::factory()->create([
            'name' => 'Catering Service',
            'hourly_rate' => 40.00,
            'is_active' => true,
        ]);

        $reservationData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(1)->format('Y-m-d'),
            'start_time' => '10:00',
            'duration_hours' => 6,
            'customer_name' => 'Complex Event Organizer',
            'utilities' => [
                [
                    'id' => $this->utility1->id, // Sound System
                    'hours' => 6, // Full duration
                ],
                [
                    'id' => $this->utility2->id, // Lighting
                    'hours' => 4, // Partial duration
                ],
                [
                    'id' => $utility3->id, // Catering
                    'hours' => 2, // Setup/cleanup only
                ],
            ],
        ];

        $response = $this->actingAs($this->user)
            ->post(route('reservations.store'), $reservationData);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $reservation = Reservation::where('customer_name', 'Complex Event Organizer')->first();
        $this->assertNotNull($reservation);

        // Verify all utilities are attached with correct costs
        $this->assertEquals(3, $reservation->utilities->count());

        $soundSystem = $reservation->utilities->where('id', $this->utility1->id)->first();
        $this->assertEquals(6, $soundSystem->pivot->hours);
        $this->assertEquals(150.00, $soundSystem->pivot->cost); // 25.00 * 6

        $lighting = $reservation->utilities->where('id', $this->utility2->id)->first();
        $this->assertEquals(4, $lighting->pivot->hours);
        $this->assertEquals(120.00, $lighting->pivot->cost); // 30.00 * 4

        $catering = $reservation->utilities->where('id', $utility3->id)->first();
        $this->assertEquals(2, $catering->pivot->hours);
        $this->assertEquals(80.00, $catering->pivot->cost); // 40.00 * 2

        // Verify total cost includes field + all utility costs
        $expectedFieldCost = $this->field->hourly_rate * 6; // 50.00 * 6 = 300.00
        $expectedUtilityCost = (25.00 * 6) + (30.00 * 4) + (40.00 * 2); // 150.00 + 120.00 + 80.00 = 350.00
        $expectedTotalCost = $expectedFieldCost + $expectedUtilityCost; // 300.00 + 350.00 = 650.00
        $this->assertEquals($expectedTotalCost, $reservation->total_cost);
    }
}
