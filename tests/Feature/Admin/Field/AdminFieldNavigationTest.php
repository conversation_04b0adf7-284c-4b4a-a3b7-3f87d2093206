<?php

namespace Tests\Feature;

use App\Models\Field;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class AdminFieldNavigationTest extends TestCase
{
    use RefreshDatabase;

    protected $admin;

    protected $field;

    protected function setUp(): void
    {
        parent::setUp();

        $this->admin = User::factory()->create(['role' => 'admin']);
        $this->field = Field::factory()->create([
            'name' => 'Navigation Test Field',
            'type' => 'Soccer',
            'description' => 'Test field for navigation testing',
            'hourly_rate' => 50.00,
            'capacity' => 20,
            'status' => 'Active',
        ]);
    }

    #[Test]
    public function back_to_fields_button_works_from_show_page()
    {
        // Access the show page
        $showResponse = $this->actingAs($this->admin)
            ->get(route('admin.fields.show', $this->field));

        $showResponse->assertStatus(200);
        $showResponse->assertSee('Back to Fields');

        // Test navigation back to index
        $indexResponse = $this->actingAs($this->admin)
            ->get(route('admin.fields.index'));

        $indexResponse->assertStatus(200);
        $indexResponse->assertSee('Field Management');

        echo "✅ Back to Fields button navigation works correctly\n";
    }

    #[Test]
    public function edit_field_button_works_from_show_page()
    {
        // Access the show page
        $showResponse = $this->actingAs($this->admin)
            ->get(route('admin.fields.show', $this->field));

        $showResponse->assertStatus(200);
        $showResponse->assertSee('Edit Field');

        // Test navigation to edit page
        $editResponse = $this->actingAs($this->admin)
            ->get(route('admin.fields.edit', $this->field));

        $editResponse->assertStatus(200);
        $editResponse->assertSee('Edit Field Information');
        $editResponse->assertSee($this->field->name);

        echo "✅ Edit Field button navigation works correctly\n";
    }

    #[Test]
    public function delete_field_modal_elements_are_present()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.fields.show', $this->field));

        $response->assertStatus(200);

        // Check for delete button
        $response->assertSee('Delete Field');

        // Check for modal elements (Bootstrap modal)
        $response->assertSee('deleteModal');
        $response->assertSee('Are you sure you want to delete');
        $response->assertSee($this->field->name);
        $response->assertSee('This action cannot be undone');

        echo "✅ Delete Field modal elements are present\n";
    }

    #[Test]
    public function field_show_page_breadcrumb_navigation()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.fields.show', $this->field));

        $response->assertStatus(200);

        // Check breadcrumb elements
        $response->assertSee('breadcrumb');
        $response->assertSee('Home');
        $response->assertSee('Fields');
        $response->assertSee('Details');

        echo "✅ Breadcrumb navigation is present and correct\n";
    }

    #[Test]
    public function field_show_page_has_proper_action_buttons()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.fields.show', $this->field));

        $response->assertStatus(200);

        // Check for action buttons (currently using Tailwind styling)
        $response->assertSee('Edit Field');
        $response->assertSee('Delete Field');
        $response->assertSee('Back to Fields');

        // Check that buttons are functional (have proper routes)
        $response->assertSee(route('admin.fields.edit', $this->field));
        $response->assertSee(route('admin.fields.index'));
        $response->assertSee('confirmDelete');

        echo "✅ Action buttons are present and functional\n";
    }

    #[Test]
    public function complete_navigation_flow_works()
    {
        // Start from index
        $indexResponse = $this->actingAs($this->admin)
            ->get(route('admin.fields.index'));
        $indexResponse->assertStatus(200);
        echo "✅ Step 1: Index page loads\n";

        // Navigate to show page (simulating View button click)
        $showResponse = $this->actingAs($this->admin)
            ->get(route('admin.fields.show', $this->field));
        $showResponse->assertStatus(200);
        $showResponse->assertSee($this->field->name);
        echo "✅ Step 2: Show page loads from View button\n";

        // Navigate to edit page (simulating Edit button click)
        $editResponse = $this->actingAs($this->admin)
            ->get(route('admin.fields.edit', $this->field));
        $editResponse->assertStatus(200);
        $editResponse->assertSee('Edit Field Information');
        echo "✅ Step 3: Edit page loads from Edit button\n";

        // Navigate back to show page (simulating View Field button from edit)
        $backToShowResponse = $this->actingAs($this->admin)
            ->get(route('admin.fields.show', $this->field));
        $backToShowResponse->assertStatus(200);
        echo "✅ Step 4: Back to show page works\n";

        // Navigate back to index (simulating Back to Fields button)
        $backToIndexResponse = $this->actingAs($this->admin)
            ->get(route('admin.fields.index'));
        $backToIndexResponse->assertStatus(200);
        echo "✅ Step 5: Back to index works\n";

        echo "✅ Complete navigation flow works perfectly\n";
    }
}
